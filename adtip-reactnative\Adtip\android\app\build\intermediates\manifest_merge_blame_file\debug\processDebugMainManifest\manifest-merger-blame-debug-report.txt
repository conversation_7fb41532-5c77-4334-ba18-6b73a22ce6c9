1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.adtip.app.adtip_app"
4    android:versionCode="30002"
5    android:versionName="31.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!-- BASIC NETWORK PERMISSIONS -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:5:5-67
11-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:6:5-79
12-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- WHATSAPP-LIKE CALLING PERMISSIONS - Minimal set -->
13-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:7:5-76
13-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:7:22-73
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:10:5-77
14-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:10:22-74
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:11:5-71
15-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:11:22-68
16    <uses-permission android:name="android.permission.CAMERA" />
16-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:12:5-65
16-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:12:22-62
17    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
17-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:13:5-80
17-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:13:22-77
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
18-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:14:5-120
18-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:14:22-85
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" /> <!-- VIDEOSDK & OVERLAY PERMISSIONS -->
19-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:15:5-116
19-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:15:22-81
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:18:5-78
20-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:18:22-75
21    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- BLUETOOTH PERMISSIONS -->
21-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:19:5-68
21-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:19:22-65
22    <uses-permission
22-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:22:5-95
23        android:name="android.permission.BLUETOOTH"
23-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:22:22-65
24        android:maxSdkVersion="30" />
24-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:22:66-92
25    <uses-permission
25-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:23:5-101
26        android:name="android.permission.BLUETOOTH_ADMIN"
26-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:23:22-71
27        android:maxSdkVersion="30" />
27-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:23:72-98
28    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
28-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:24:5-76
28-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:24:22-73
29    <uses-permission
29-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:25:5-27:44
30        android:name="android.permission.BLUETOOTH_SCAN"
30-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:25:22-70
31        android:usesPermissionFlags="neverForLocation" /> <!-- LOCATION PERMISSIONS -->
31-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:26:22-68
32    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
32-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:30:5-79
32-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:30:22-76
33    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- STORAGE PERMISSIONS -->
33-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:31:5-81
33-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:31:22-78
34    <uses-permission
34-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:34:5-107
35        android:name="android.permission.READ_EXTERNAL_STORAGE"
35-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:34:22-77
36        android:maxSdkVersion="32" />
36-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:34:78-104
37    <uses-permission
37-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:35:5-39:24
38        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
38-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:35:22-78
39        android:maxSdkVersion="28" />
39-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:36:22-48
40    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
40-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:40:5-102
40-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:40:22-72
41    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
41-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:41:5-102
41-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:41:22-72
42    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- NOTIFICATIONS -->
42-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:42:5-103
42-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:42:22-73
43    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
43-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:45:5-104
43-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:45:22-74
44    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
44-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:46:5-81
44-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:46:22-78
45    <uses-permission android:name="android.permission.VIBRATE" />
45-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:47:5-66
45-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:47:22-63
46    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
46-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:48:5-88
46-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:48:22-85
47    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" /> <!-- CALLLEEP SELF-MANAGED PERMISSIONS -->
47-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:49:5-75
47-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:49:22-72
48    <uses-permission android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE" />
48-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:52:5-90
48-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:52:22-87
49    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- ADD_VOICEMAIL removed - not used by core app functionality -->
49-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:53:5-69
49-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:53:22-66
50    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" /> <!-- Camera features -->
50-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:55:5-81
50-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:55:22-78
51    <uses-feature
51-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:58:5-85
52        android:name="android.hardware.camera"
52-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:58:19-57
53        android:required="false" />
53-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:58:58-82
54    <uses-feature
54-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:59:5-95
55        android:name="android.hardware.camera.autofocus"
55-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:59:19-67
56        android:required="false" />
56-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:59:68-92
57    <uses-feature
57-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:60:5-89
58        android:name="android.hardware.microphone"
58-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:60:19-61
59        android:required="false" /> <!-- AD TRACKING -->
59-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:60:62-86
60    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
60-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:63:5-79
60-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:63:22-76
61    <uses-permission
61-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:72:5-75
62        android:name="android.permission.READ_PHONE_STATE"
62-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:72:22-72
63        android:maxSdkVersion="29" />
63-->[:react-native-callkeep] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-callkeep\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-35
64
65    <queries>
65-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
66        <intent>
66-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
67            <action android:name="android.media.action.IMAGE_CAPTURE" />
67-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
67-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
68        </intent>
69        <intent>
69-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:17:9-19:18
70            <action android:name="android.intent.action.MAIN" />
70-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:17-69
70-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:25-66
71        </intent>
72        <intent>
72-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:11:9-17:18
73            <action android:name="android.intent.action.VIEW" />
73-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
73-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
74
75            <data
75-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
76                android:mimeType="*/*"
77                android:scheme="*" />
77-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
78        </intent>
79        <intent>
79-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:18:9-27:18
80            <action android:name="android.intent.action.VIEW" />
80-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
80-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
81
82            <category android:name="android.intent.category.BROWSABLE" />
82-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
82-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
83
84            <data
84-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
85                android:host="pay"
86                android:mimeType="*/*"
87                android:scheme="upi" />
87-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
88        </intent>
89        <intent>
89-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:31:9-35:18
90            <action android:name="android.intent.action.SEND" />
90-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:13-65
90-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:21-62
91
92            <data android:mimeType="*/*" />
92-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
93        </intent>
94        <intent>
94-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:36:9-38:18
95            <action android:name="rzp.device_token.share" />
95-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:13-61
95-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:21-58
96        </intent> <!-- For browser content -->
97        <intent>
97-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
98            <action android:name="android.intent.action.VIEW" />
98-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
98-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
99
100            <category android:name="android.intent.category.BROWSABLE" />
100-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
100-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
101
102            <data android:scheme="https" />
102-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
102-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
103        </intent> <!-- End of browser content -->
104        <!-- For CustomTabsService -->
105        <intent>
105-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
106            <action android:name="android.support.customtabs.action.CustomTabsService" />
106-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
106-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
107        </intent> <!-- End of CustomTabsService -->
108        <!-- For MRAID capabilities -->
109        <intent>
109-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
110            <action android:name="android.intent.action.INSERT" />
110-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
110-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
111
112            <data android:mimeType="vnd.android.cursor.dir/event" />
112-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
113        </intent>
114        <intent>
114-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
115            <action android:name="android.intent.action.VIEW" />
115-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
115-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
116
117            <data android:scheme="sms" />
117-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
117-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
118        </intent>
119        <intent>
119-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
120            <action android:name="android.intent.action.DIAL" />
120-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
120-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
121
122            <data android:path="tel:" />
122-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
123        </intent>
124        <intent>
124-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:23:9-25:18
125            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
125-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:13-86
125-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:21-83
126        </intent>
127    </queries>
128
129    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
129-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:11:5-98
129-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:11:22-95
130    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
130-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:12:5-14:47
130-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:13:9-62
131    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
131-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
131-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
132    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
132-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
132-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
133    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
133-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
133-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
134    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
134-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
134-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
135    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
135-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
135-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
136
137    <uses-feature
137-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
138        android:glEsVersion="0x00020000"
138-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
139        android:required="true" />
139-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
140
141    <permission
141-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
142        android:name="com.adtip.app.adtip_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
142-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
143        android:protectionLevel="signature" />
143-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
144
145    <uses-permission android:name="com.adtip.app.adtip_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
145-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
145-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
146    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
146-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:16:5-79
146-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:16:22-76
147    <uses-permission
147-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:17:5-19:38
148        android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS"
148-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:18:9-73
149        android:maxSdkVersion="30" /> <!-- For Xiaomi devices to enable heads-up notifications as default (https://github.com/invertase/notifee/issues/296) -->
149-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:19:9-35
150    <uses-permission
150-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:21:5-23:38
151        android:name="android.permission.ACCESS_NOTIFICATION_POLICY"
151-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:22:9-69
152        android:minSdkVersion="23" />
152-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:23:9-35
153
154    <application
154-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:77:5-214:19
155        android:name="com.adtip.app.adtip_app.MainApplication"
155-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:78:9-40
156        android:allowBackup="false"
156-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:82:9-36
157        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
157-->[androidx.core:core:1.16.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e4f7aaf1dd3251b595a6d29fbf072499\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
158        android:debuggable="true"
159        android:extractNativeLibs="false"
160        android:icon="@mipmap/ic_launcher"
160-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:80:9-43
161        android:label="@string/app_name"
161-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:79:9-41
162        android:roundIcon="@mipmap/ic_launcher_round"
162-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:81:9-54
163        android:supportsRtl="true"
163-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:84:9-35
164        android:theme="@style/AppTheme"
164-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:83:9-40
165        android:usesCleartextTraffic="true" >
165-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:85:9-44
166        <meta-data
166-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:88:9-90:36
167            android:name="android.app.extract_native_libs"
167-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:89:13-59
168            android:value="true" /> <!-- AdMob App ID - PubScale Only -->
168-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:90:13-33
169        <!-- Note: Simplified ad system uses only PubScale ad units -->
170        <meta-data
170-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:94:9-97:45
171            android:name="com.google.android.gms.ads.APPLICATION_ID"
171-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:95:13-69
172            android:value="ca-app-pub-3206456546664189~6654042212" /> <!-- VIDEOSDK FOREGROUND SERVICE CONFIGURATION -->
172-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:96:13-67
173        <meta-data
173-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:100:9-102:52
174            android:name="live.videosdk.rnfgservice.notification_channel_name"
174-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:101:13-79
175            android:value="Meeting Notification" />
175-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:102:13-49
176        <meta-data
176-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:103:9-105:82
177            android:name="live.videosdk.rnfgservice.notification_channel_description"
177-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:104:13-86
178            android:value="Whenever meeting started notification will appear." />
178-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:105:13-79
179        <meta-data
179-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:106:9-108:63
180            android:name="live.videosdk.rnfgservice.notification_color"
180-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:107:13-72
181            android:resource="@android:color/holo_red_dark" /> <!-- Firebase messaging default notification channel -->
181-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:108:13-60
182        <meta-data
182-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:111:9-114:45
183            android:name="com.google.firebase.messaging.default_notification_channel_id"
183-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:112:13-89
184            android:value="adtip_call_channel" />
184-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:113:13-47
185
186        <activity
186-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:116:9-173:20
187            android:name="com.adtip.app.adtip_app.MainActivity"
187-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:117:13-41
188            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
188-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:119:13-122
189            android:exported="true"
189-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:122:13-36
190            android:label="@string/app_name"
190-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:118:13-45
191            android:launchMode="singleTask"
191-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:120:13-44
192            android:showWhenLocked="true"
192-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:123:13-42
193            android:turnScreenOn="true"
193-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:124:13-40
194            android:windowSoftInputMode="adjustPan" >
194-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:121:13-52
195            <intent-filter>
195-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:125:13-128:29
196                <action android:name="android.intent.action.MAIN" />
196-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:17-69
196-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:25-66
197
198                <category android:name="android.intent.category.LAUNCHER" />
198-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:127:17-77
198-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:127:27-74
199            </intent-filter>
200
201            <!-- Custom URL Scheme Deep Links -->
202            <intent-filter>
202-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:131:13-136:29
203                <action android:name="android.intent.action.VIEW" />
203-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
203-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
204
205                <category android:name="android.intent.category.DEFAULT" />
205-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
205-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
206                <category android:name="android.intent.category.BROWSABLE" />
206-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
206-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
207
208                <data android:scheme="adtip" />
208-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
208-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
209            </intent-filter>
210
211            <!-- Universal Links / App Links for adtip.in -->
212            <intent-filter android:autoVerify="true" >
212-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:139:13-145:29
212-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:139:28-53
213                <action android:name="android.intent.action.VIEW" />
213-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
213-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
214
215                <category android:name="android.intent.category.DEFAULT" />
215-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
215-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
216                <category android:name="android.intent.category.BROWSABLE" />
216-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
216-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
217
218                <data
218-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
219                    android:host="adtip.in"
220                    android:scheme="https" />
220-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
221            </intent-filter>
222
223            <!-- Universal Links for www.adtip.in -->
224            <intent-filter android:autoVerify="true" >
224-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:148:13-154:29
224-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:148:28-53
225                <action android:name="android.intent.action.VIEW" />
225-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
225-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
226
227                <category android:name="android.intent.category.DEFAULT" />
227-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
227-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
228                <category android:name="android.intent.category.BROWSABLE" />
228-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
228-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
229
230                <data
230-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
231                    android:host="www.adtip.in"
232                    android:scheme="https" />
232-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
233            </intent-filter>
234
235            <!-- Universal Links for app.adtip.in -->
236            <intent-filter android:autoVerify="true" >
236-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:157:13-163:29
236-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:157:28-53
237                <action android:name="android.intent.action.VIEW" />
237-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
237-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
238
239                <category android:name="android.intent.category.DEFAULT" />
239-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
239-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
240                <category android:name="android.intent.category.BROWSABLE" />
240-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
240-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
241
242                <data
242-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
243                    android:host="app.adtip.in"
244                    android:scheme="https" />
244-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
245            </intent-filter>
246            <intent-filter android:autoVerify="true" >
246-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:165:13-171:29
246-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:165:28-53
247                <action android:name="android.intent.action.VIEW" />
247-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
247-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
248
249                <category android:name="android.intent.category.DEFAULT" />
249-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
249-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
250                <category android:name="android.intent.category.BROWSABLE" />
250-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
250-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
251
252                <data android:scheme="https" />
252-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
252-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
253                <data android:host="adtip.com" />
253-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
254            </intent-filter>
255        </activity> <!-- VIDEOSDK FOREGROUND SERVICE -->
256        <service
256-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:176:9-179:40
257            android:name="live.videosdk.rnfgservice.ForegroundService"
257-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:177:13-71
258            android:exported="false"
258-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:179:13-37
259            android:foregroundServiceType="camera|microphone" /> <!-- Notifee's foreground service declaration for camera and microphone -->
259-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:178:13-62
260        <service
260-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:9-155
261            android:name="app.notifee.core.ForegroundService"
261-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:18-67
262            android:exported="false"
262-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:128-152
263            android:foregroundServiceType="camera|microphone|phoneCall|shortService" /> <!-- CALLLEEP CONNECTION SERVICE -->
263-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:182:68-127
264        <service
264-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:185:9-197:19
265            android:name="io.wazo.callkeep.VoiceConnectionService"
265-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:186:13-67
266            android:exported="true"
266-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:190:13-36
267            android:foregroundServiceType="phoneCall|camera|microphone"
267-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:189:13-72
268            android:label="VoiceConnectionService"
268-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:187:13-51
269            android:permission="android.permission.BIND_TELECOM_CONNECTION_SERVICE" >
269-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:188:13-84
270            <intent-filter>
270-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:191:13-193:29
271                <action android:name="android.telecom.ConnectionService" />
271-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:192:17-76
271-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:192:25-73
272            </intent-filter>
273
274            <meta-data
274-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:194:13-196:69
275                android:name="android.telecom.CONNECTION_SERVICE"
275-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:195:17-66
276                android:value="android.telecom.ConnectionService" />
276-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:196:17-66
277        </service>
278        <service android:name="io.wazo.callkeep.RNCallKeepBackgroundMessagingService" /> <!-- ADTIP FIREBASE MESSAGING SERVICE -->
278-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:198:9-89
278-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:198:18-86
279        <service
279-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:201:9-207:19
280            android:name="com.adtip.app.adtip_app.AdtipFirebaseMessagingService"
280-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:202:13-81
281            android:exported="false" >
281-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:203:13-37
282            <intent-filter>
282-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:204:13-206:29
283                <action android:name="com.google.firebase.MESSAGING_EVENT" />
283-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:17-78
283-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:25-75
284            </intent-filter>
285        </service> <!-- ADTIP CALL RINGING SERVICE -->
286        <service
286-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:210:9-212:40
287            android:name="com.adtip.app.adtip_app.CallRingingService"
287-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:211:13-70
288            android:exported="false" />
288-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:212:13-37
289
290        <meta-data
290-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:37
291            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
291-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-81
292            android:value="false" />
292-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-34
293        <meta-data
293-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-20:36
294            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
294-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-83
295            android:value="true" />
295-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-33
296        <meta-data
296-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-23:36
297            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
297-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-79
298            android:value="true" />
298-->[:react-native-google-mobile-ads] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-33
299
300        <provider
300-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
301            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
301-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
302            android:authorities="com.adtip.app.adtip_app.fileprovider"
302-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
303            android:exported="false"
303-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
304            android:grantUriPermissions="true" >
304-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
305            <meta-data
305-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
306                android:name="android.support.FILE_PROVIDER_PATHS"
306-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
307                android:resource="@xml/file_provider_paths" />
307-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
308        </provider>
309        <provider
309-->[:notifee_react-native] F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:40
310            android:name="io.invertase.notifee.NotifeeInitProvider"
310-->[:notifee_react-native] F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-68
311            android:authorities="com.adtip.app.adtip_app.notifee-init-provider"
311-->[:notifee_react-native] F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-73
312            android:exported="false"
312-->[:notifee_react-native] F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
313            android:initOrder="-100" />
313-->[:notifee_react-native] F:\A1\adtip-reactnative\Adtip\node_modules\@notifee\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
314
315        <meta-data
315-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:36
316            android:name="firebase_analytics_collection_enabled"
316-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-65
317            android:value="true" />
317-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
318        <meta-data
318-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:37
319            android:name="firebase_analytics_collection_deactivated"
319-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-69
320            android:value="false" />
320-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-34
321        <meta-data
321-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-20:36
322            android:name="google_analytics_adid_collection_enabled"
322-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-68
323            android:value="true" />
323-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-33
324        <meta-data
324-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-23:36
325            android:name="google_analytics_ssaid_collection_enabled"
325-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-69
326            android:value="true" />
326-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-33
327        <meta-data
327-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-26:36
328            android:name="google_analytics_automatic_screen_reporting_enabled"
328-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-79
329            android:value="true" />
329-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-33
330        <meta-data
330-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-29:36
331            android:name="google_analytics_default_allow_analytics_storage"
331-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-76
332            android:value="true" />
332-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-33
333        <meta-data
333-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:9-32:36
334            android:name="google_analytics_default_allow_ad_storage"
334-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-69
335            android:value="true" />
335-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-33
336        <meta-data
336-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-35:36
337            android:name="google_analytics_default_allow_ad_user_data"
337-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-71
338            android:value="true" />
338-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-33
339        <meta-data
339-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:9-38:36
340            android:name="google_analytics_default_allow_ad_personalization_signals"
340-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-85
341            android:value="true" /> <!-- Disable crashlytics by default so we can custom init with CrashlyticsNdk support -->
341-->[:react-native-firebase_analytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-33
342        <meta-data
342-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-12:37
343            android:name="firebase_crashlytics_collection_enabled"
343-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-67
344            android:value="false" />
344-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-34
345
346        <provider
346-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:38
347            android:name="io.invertase.firebase.crashlytics.ReactNativeFirebaseCrashlyticsInitProvider"
347-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-104
348            android:authorities="com.adtip.app.adtip_app.reactnativefirebasecrashlyticsinitprovider"
348-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-94
349            android:exported="false"
349-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
350            android:initOrder="98" />
350-->[:react-native-firebase_crashlytics] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-35
351
352        <service
352-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
353            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
353-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
354            android:exported="false" />
354-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
355        <service
355-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
356            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
356-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
357            android:exported="false" >
357-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
358            <intent-filter>
358-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:204:13-206:29
359                <action android:name="com.google.firebase.MESSAGING_EVENT" />
359-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:17-78
359-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:25-75
360            </intent-filter>
361        </service>
362
363        <receiver
363-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
364            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
364-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
365            android:exported="true"
365-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
366            android:permission="com.google.android.c2dm.permission.SEND" >
366-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
367            <intent-filter>
367-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
368                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
368-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
368-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
369            </intent-filter>
370        </receiver>
371
372        <meta-data
372-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:37
373            android:name="delivery_metrics_exported_to_big_query_enabled"
373-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-74
374            android:value="false" />
374-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-34
375        <meta-data
375-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:36
376            android:name="firebase_messaging_auto_init_enabled"
376-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-64
377            android:value="true" />
377-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-33
378        <meta-data
378-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:37
379            android:name="firebase_messaging_notification_delegation_enabled"
379-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-78
380            android:value="false" />
380-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-34
381        <meta-data
381-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:47
382            android:name="com.google.firebase.messaging.default_notification_color"
382-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-84
383            android:resource="@color/white" />
383-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-44
384        <meta-data
384-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
385            android:name="app_data_collection_default_enabled"
385-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
386            android:value="true" />
386-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
387
388        <service
388-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
389            android:name="com.google.firebase.components.ComponentDiscoveryService"
389-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
390            android:directBootAware="true"
390-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
391            android:exported="false" >
391-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
392            <meta-data
392-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
393                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
393-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
394                android:value="com.google.firebase.components.ComponentRegistrar" />
394-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
395            <meta-data
395-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
396                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
396-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
397                android:value="com.google.firebase.components.ComponentRegistrar" />
397-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
398            <meta-data
398-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
399                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
399-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
400                android:value="com.google.firebase.components.ComponentRegistrar" />
400-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
401            <meta-data
401-->[com.google.firebase:firebase-crashlytics-ndk:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:32:13-34:85
402                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ndk.CrashlyticsNdkRegistrar"
402-->[com.google.firebase:firebase-crashlytics-ndk:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:33:17-122
403                android:value="com.google.firebase.components.ComponentRegistrar" />
403-->[com.google.firebase:firebase-crashlytics-ndk:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\aeb8af0b6421a1d3b2ea7118ef2989f3\transformed\jetified-firebase-crashlytics-ndk-19.4.4\AndroidManifest.xml:34:17-82
404            <meta-data
404-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
405                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
405-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
406                android:value="com.google.firebase.components.ComponentRegistrar" />
406-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
407            <meta-data
407-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
408                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
408-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:19:17-115
409                android:value="com.google.firebase.components.ComponentRegistrar" />
409-->[com.google.firebase:firebase-crashlytics:19.4.4] F:\R17DevTools\.gradle\caches\8.13\transforms\1756d0e485a053b668d9b50ad4f3232e\transformed\jetified-firebase-crashlytics-19.4.4\AndroidManifest.xml:20:17-82
410            <meta-data
410-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
411                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
411-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
412                android:value="com.google.firebase.components.ComponentRegistrar" />
412-->[com.google.android.gms:play-services-measurement-api:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\da392714e2b4001ad5e92291804d3aa6\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
413            <meta-data
413-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
414                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
414-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
415                android:value="com.google.firebase.components.ComponentRegistrar" />
415-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
416            <meta-data
416-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
417                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
417-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
418                android:value="com.google.firebase.components.ComponentRegistrar" />
418-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
419            <meta-data
419-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
420                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
420-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
421                android:value="com.google.firebase.components.ComponentRegistrar" />
421-->[com.google.firebase:firebase-database:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\2a460d6b3936d3e351c6512e5610dcf7\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
422            <meta-data
422-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
423                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
423-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
424                android:value="com.google.firebase.components.ComponentRegistrar" />
424-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
425            <meta-data
425-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
426                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
426-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
427                android:value="com.google.firebase.components.ComponentRegistrar" />
427-->[com.google.firebase:firebase-firestore:25.1.4] F:\R17DevTools\.gradle\caches\8.13\transforms\0462f5e85e6a2044c6a7a07e46b26c72\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
428            <meta-data
428-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
429                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
429-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
430                android:value="com.google.firebase.components.ComponentRegistrar" />
430-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
431            <meta-data
431-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
432                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
432-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
433                android:value="com.google.firebase.components.ComponentRegistrar" />
433-->[com.google.firebase:firebase-storage:21.0.2] F:\R17DevTools\.gradle\caches\8.13\transforms\0d9b7021a76b8c370dc1a55cf6e33d91\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
434            <meta-data
434-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
435                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
435-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
436                android:value="com.google.firebase.components.ComponentRegistrar" />
436-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
437            <meta-data
437-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
438                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
438-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
439                android:value="com.google.firebase.components.ComponentRegistrar" />
439-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
440            <meta-data
440-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
441                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
441-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
442                android:value="com.google.firebase.components.ComponentRegistrar" />
442-->[com.google.firebase:firebase-installations:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\943c4eb5cff9afe2e09fe6fa01f0c45d\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
443            <meta-data
443-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
444                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
444-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
445                android:value="com.google.firebase.components.ComponentRegistrar" />
445-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
446            <meta-data
446-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
447                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
447-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
448                android:value="com.google.firebase.components.ComponentRegistrar" />
448-->[com.google.firebase:firebase-appcheck:18.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\e25a4e9ce0a57f7ae5d2347b9eeb0127\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
449            <meta-data
449-->[com.google.firebase:firebase-common-ktx:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
450                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
450-->[com.google.firebase:firebase-common-ktx:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
451                android:value="com.google.firebase.components.ComponentRegistrar" />
451-->[com.google.firebase:firebase-common-ktx:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7154a9c5f3c74624ae64a9d79896bb9e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
452            <meta-data
452-->[com.google.firebase:firebase-datatransport:19.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
453                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
453-->[com.google.firebase:firebase-datatransport:19.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
454                android:value="com.google.firebase.components.ComponentRegistrar" />
454-->[com.google.firebase:firebase-datatransport:19.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8bcdce69c2e38ab05a17e87b8e240c89\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
455            <meta-data
455-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
456                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
456-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
457                android:value="com.google.firebase.components.ComponentRegistrar" />
457-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
458        </service>
459
460        <provider
460-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
461            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
461-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
462            android:authorities="com.adtip.app.adtip_app.reactnativefirebaseappinitprovider"
462-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
463            android:exported="false"
463-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
464            android:initOrder="99" />
464-->[:react-native-firebase_app] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
465        <provider
465-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-26:20
466            android:name="com.reactnative.ivpusic.imagepicker.IvpusicImagePickerFileProvider"
466-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-94
467            android:authorities="com.adtip.app.adtip_app.provider"
467-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-60
468            android:exported="false"
468-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
469            android:grantUriPermissions="true" >
469-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-47
470            <meta-data
470-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
471                android:name="android.support.FILE_PROVIDER_PATHS"
471-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
472                android:resource="@xml/ivpusic_imagepicker_provider_paths" />
472-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
473        </provider>
474
475        <activity
475-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:9-30:72
476            android:name="com.yalantis.ucrop.UCropActivity"
476-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-60
477            android:theme="@style/Theme.AppCompat.Light.NoActionBar" /> <!-- Prompt Google Play services to install the backported photo picker module -->
477-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-69
478        <service
478-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-44:19
479            android:name="com.google.android.gms.metadata.ModuleDependencies"
479-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-78
480            android:enabled="false"
480-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-36
481            android:exported="false" >
481-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-37
482            <intent-filter>
482-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-39:29
483                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
483-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-94
483-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:25-91
484            </intent-filter>
485
486            <meta-data
486-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-43:36
487                android:name="photopicker_activity:0:required"
487-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:17-63
488                android:value="" />
488-->[:react-native-image-crop-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:17-33
489        </service>
490
491        <provider
491-->[:react-native-image-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
492            android:name="com.imagepicker.ImagePickerProvider"
492-->[:react-native-image-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-63
493            android:authorities="com.adtip.app.adtip_app.imagepickerprovider"
493-->[:react-native-image-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
494            android:exported="false"
494-->[:react-native-image-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
495            android:grantUriPermissions="true" >
495-->[:react-native-image-picker] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
496            <meta-data
496-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
497                android:name="android.support.FILE_PROVIDER_PATHS"
497-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
498                android:resource="@xml/imagepicker_provider_paths" />
498-->[:react-native-webview] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
499        </provider>
500
501        <activity
501-->[:react-native-razorpay] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:86
502            android:name="com.razorpay.CheckoutActivity"
502-->[:react-native-razorpay] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-57
503            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
503-->[:react-native-razorpay] F:\A1\adtip-reactnative\Adtip\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-83
504            android:exported="false"
504-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:45:13-37
505            android:theme="@style/CheckoutTheme" >
505-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:46:13-49
506            <intent-filter>
506-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:47:13-49:29
507                <action android:name="android.intent.action.MAIN" />
507-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:17-69
507-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:126:25-66
508            </intent-filter>
509        </activity>
510        <activity
510-->[com.facebook.react:react-android:0.79.2] F:\R17DevTools\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
511            android:name="com.facebook.react.devsupport.DevSettingsActivity"
511-->[com.facebook.react:react-android:0.79.2] F:\R17DevTools\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
512            android:exported="false" />
512-->[com.facebook.react:react-android:0.79.2] F:\R17DevTools\.gradle\caches\8.13\transforms\70054bad7b567e49310c48ab88316206\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
513        <activity
513-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:23:9-27:65
514            android:name="com.pubscale.sdkone.offerwall.ui.OfferWallActivity"
514-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:24:13-78
515            android:configChanges="orientation|screenSize"
515-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:25:13-59
516            android:exported="false"
516-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:26:13-37
517            android:theme="@style/Theme.PubScaleOfferWallSDK" />
517-->[com.pubscale.sdkone:offerwall:1.0.11] F:\R17DevTools\.gradle\caches\8.13\transforms\f3d40c7581012cec34cbd9e34b6e115f\transformed\jetified-offerwall-1.0.11\AndroidManifest.xml:27:13-62
518
519        <receiver
519-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
520            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
520-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
521            android:exported="true"
521-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
522            android:permission="com.google.android.c2dm.permission.SEND" >
522-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
523            <intent-filter>
523-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
524                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
524-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
524-->[:react-native-firebase_messaging] F:\A1\adtip-reactnative\Adtip\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
525            </intent-filter>
526
527            <meta-data
527-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
528                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
528-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
529                android:value="true" />
529-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
530        </receiver>
531        <!--
532             FirebaseMessagingService performs security checks at runtime,
533             but set to not exported to explicitly avoid allowing another app to call it.
534        -->
535        <service
535-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
536            android:name="com.google.firebase.messaging.FirebaseMessagingService"
536-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
537            android:directBootAware="true"
537-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
538            android:exported="false" >
538-->[com.google.firebase:firebase-messaging:24.1.1] F:\R17DevTools\.gradle\caches\8.13\transforms\81058a2b515ff64e794fc91691a121e3\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
539            <intent-filter android:priority="-500" >
539-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:204:13-206:29
540                <action android:name="com.google.firebase.MESSAGING_EVENT" />
540-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:17-78
540-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:205:25-75
541            </intent-filter>
542        </service>
543
544        <provider
544-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:52:9-60:20
545            android:name="androidx.startup.InitializationProvider"
545-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:53:13-67
546            android:authorities="com.adtip.app.adtip_app.androidx-startup"
546-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:54:13-68
547            android:exported="false" >
547-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:55:13-37
548            <meta-data
548-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:57:13-59:52
549                android:name="com.razorpay.RazorpayInitializer"
549-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:58:17-64
550                android:value="androidx.startup" />
550-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:59:17-49
551            <meta-data
551-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
552                android:name="androidx.work.WorkManagerInitializer"
552-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
553                android:value="androidx.startup" />
553-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
554            <meta-data
554-->[androidx.emoji2:emoji2:1.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
555                android:name="androidx.emoji2.text.EmojiCompatInitializer"
555-->[androidx.emoji2:emoji2:1.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
556                android:value="androidx.startup" />
556-->[androidx.emoji2:emoji2:1.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\c02c6a58638664cbfa4f398f77214d5c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
557            <meta-data
557-->[androidx.lifecycle:lifecycle-process:2.8.7] F:\R17DevTools\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
558                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
558-->[androidx.lifecycle:lifecycle-process:2.8.7] F:\R17DevTools\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
559                android:value="androidx.startup" />
559-->[androidx.lifecycle:lifecycle-process:2.8.7] F:\R17DevTools\.gradle\caches\8.13\transforms\88ef7ff10d1cfe00bba6c2d4e1214614\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
560            <meta-data
560-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
561                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
561-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
562                android:value="androidx.startup" />
562-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
563        </provider>
564
565        <activity
565-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:62:9-65:75
566            android:name="com.razorpay.MagicXActivity"
566-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:63:13-55
567            android:exported="false"
567-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:64:13-37
568            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
568-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:65:13-72
569
570        <meta-data
570-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:67:9-69:58
571            android:name="com.razorpay.plugin.googlepay_all"
571-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:68:13-61
572            android:value="com.razorpay.RzpGpayMerged" />
572-->[com.razorpay:standard-core:1.6.53] F:\R17DevTools\.gradle\caches\8.13\transforms\f0a42cfad74ac1af70df5b18952e3a75\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:69:13-55
573
574        <activity
574-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
575            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
575-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
576            android:excludeFromRecents="true"
576-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
577            android:exported="true"
577-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
578            android:launchMode="singleTask"
578-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
579            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
579-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
580            <intent-filter>
580-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
581                <action android:name="android.intent.action.VIEW" />
581-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
581-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
582
583                <category android:name="android.intent.category.DEFAULT" />
583-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
583-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
584                <category android:name="android.intent.category.BROWSABLE" />
584-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
584-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
585
586                <data
586-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
587                    android:host="firebase.auth"
588                    android:path="/"
589                    android:scheme="genericidp" />
589-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
590            </intent-filter>
591        </activity>
592        <activity
592-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
593            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
593-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
594            android:excludeFromRecents="true"
594-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
595            android:exported="true"
595-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
596            android:launchMode="singleTask"
596-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
597            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
597-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
598            <intent-filter>
598-->[com.google.firebase:firebase-auth:23.2.1] F:\R17DevTools\.gradle\caches\8.13\transforms\d6b26d75f3c4242a3fd6d0a5933b17d8\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
599                <action android:name="android.intent.action.VIEW" />
599-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:17-69
599-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:132:25-66
600
601                <category android:name="android.intent.category.DEFAULT" />
601-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:17-76
601-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:133:27-73
602                <category android:name="android.intent.category.BROWSABLE" />
602-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:17-78
602-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:134:27-75
603
604                <data
604-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:17-48
605                    android:host="firebase.auth"
606                    android:path="/"
607                    android:scheme="recaptcha" />
607-->F:\A1\adtip-reactnative\Adtip\android\app\src\main\AndroidManifest.xml:135:23-45
608            </intent-filter>
609        </activity>
610
611        <service
611-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
612            android:name="com.google.firebase.sessions.SessionLifecycleService"
612-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
613            android:enabled="true"
613-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
614            android:exported="false" />
614-->[com.google.firebase:firebase-sessions:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\e3d2eaa1583aa636ad7200f2b7ab13e0\transformed\jetified-firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
615
616        <meta-data
616-->[com.github.bumptech.glide:okhttp3-integration:4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:10:9-12:43
617            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
617-->[com.github.bumptech.glide:okhttp3-integration:4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:11:13-84
618            android:value="GlideModule" />
618-->[com.github.bumptech.glide:okhttp3-integration:4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\819885695693bbcd94c9388920867bc1\transformed\jetified-okhttp3-integration-4.14.2\AndroidManifest.xml:12:13-40
619
620        <service
620-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
621            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
621-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
622            android:directBootAware="true"
622-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
623            android:exported="false" >
623-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
624            <meta-data
624-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
625                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
625-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
626                android:value="com.google.firebase.components.ComponentRegistrar" />
626-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] F:\R17DevTools\.gradle\caches\8.13\transforms\72b0f839adbd62970af4fc893cad0618\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
627            <meta-data
627-->[com.google.mlkit:vision-common:17.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
628                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
628-->[com.google.mlkit:vision-common:17.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
629                android:value="com.google.firebase.components.ComponentRegistrar" />
629-->[com.google.mlkit:vision-common:17.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\8ac0cc6b1d80a9a5d39c72b858381152\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
630            <meta-data
630-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
631                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
631-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
632                android:value="com.google.firebase.components.ComponentRegistrar" />
632-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
633        </service>
634
635        <provider
635-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
636            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
636-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
637            android:authorities="com.adtip.app.adtip_app.mlkitinitprovider"
637-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
638            android:exported="false"
638-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
639            android:initOrder="99" />
639-->[com.google.mlkit:common:18.11.0] F:\R17DevTools\.gradle\caches\8.13\transforms\9abbf9a46284e5d98ed99dd592794407\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
640
641        <service
641-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
642            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
642-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
643            android:exported="false" >
643-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
644            <meta-data
644-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
645                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
645-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
646                android:value="cct" />
646-->[com.google.android.datatransport:transport-backend-cct:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\7b5b5af68370d2b9a9c88be9dd6fee4c\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
647        </service> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
648        <activity
648-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
649            android:name="com.google.android.gms.ads.AdActivity"
649-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
650            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
650-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
651            android:exported="false"
651-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
652            android:theme="@android:style/Theme.Translucent" />
652-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
653
654        <provider
654-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
655            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
655-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
656            android:authorities="com.adtip.app.adtip_app.mobileadsinitprovider"
656-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
657            android:exported="false"
657-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
658            android:initOrder="100" />
658-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
659
660        <service
660-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
661            android:name="com.google.android.gms.ads.AdService"
661-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
662            android:enabled="true"
662-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
663            android:exported="false" />
663-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
664
665        <activity
665-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
666            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
666-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
667            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
667-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
668            android:exported="false" />
668-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
669        <activity
669-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
670            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
670-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
671            android:excludeFromRecents="true"
671-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
672            android:exported="false"
672-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
673            android:launchMode="singleTask"
673-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
674            android:taskAffinity=""
674-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
675            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
675-->[com.google.android.gms:play-services-ads-api:24.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\dfa808ea662cee21aa5b55e1f60dbc52\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
676
677        <service
677-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
678            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
678-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
679            android:directBootAware="false"
679-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
680            android:enabled="@bool/enable_system_alarm_service_default"
680-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
681            android:exported="false" />
681-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
682        <service
682-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
683            android:name="androidx.work.impl.background.systemjob.SystemJobService"
683-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
684            android:directBootAware="false"
684-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
685            android:enabled="@bool/enable_system_job_service_default"
685-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
686            android:exported="true"
686-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
687            android:permission="android.permission.BIND_JOB_SERVICE" />
687-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
688        <service
688-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
689            android:name="androidx.work.impl.foreground.SystemForegroundService"
689-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
690            android:directBootAware="false"
690-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
691            android:enabled="@bool/enable_system_foreground_service_default"
691-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
692            android:exported="false" />
692-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
693
694        <receiver
694-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
695            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
695-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
696            android:directBootAware="false"
696-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
697            android:enabled="true"
697-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
698            android:exported="false" />
698-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
699        <receiver
699-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
700            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
700-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
701            android:directBootAware="false"
701-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
702            android:enabled="false"
702-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
703            android:exported="false" >
703-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
704            <intent-filter>
704-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
705                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
705-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
705-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
706                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
706-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
706-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
707            </intent-filter>
708        </receiver>
709        <receiver
709-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
710            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
710-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
711            android:directBootAware="false"
711-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
712            android:enabled="false"
712-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
713            android:exported="false" >
713-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
714            <intent-filter>
714-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
715                <action android:name="android.intent.action.BATTERY_OKAY" />
715-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
715-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
716                <action android:name="android.intent.action.BATTERY_LOW" />
716-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
716-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
717            </intent-filter>
718        </receiver>
719        <receiver
719-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
720            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
720-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
721            android:directBootAware="false"
721-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
722            android:enabled="false"
722-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
723            android:exported="false" >
723-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
724            <intent-filter>
724-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
725                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
725-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
725-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
726                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
726-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
726-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
727            </intent-filter>
728        </receiver>
729        <receiver
729-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
730            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
730-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
731            android:directBootAware="false"
731-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
732            android:enabled="false"
732-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
733            android:exported="false" >
733-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
734            <intent-filter>
734-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
735                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
735-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
735-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
736            </intent-filter>
737        </receiver>
738        <receiver
738-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
739            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
739-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
740            android:directBootAware="false"
740-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
741            android:enabled="false"
741-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
742            android:exported="false" >
742-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
743            <intent-filter>
743-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
744                <action android:name="android.intent.action.BOOT_COMPLETED" />
744-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
744-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
745                <action android:name="android.intent.action.TIME_SET" />
745-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
745-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
746                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
746-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
746-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
747            </intent-filter>
748        </receiver>
749        <receiver
749-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
750            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
750-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
751            android:directBootAware="false"
751-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
752            android:enabled="@bool/enable_system_alarm_service_default"
752-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
753            android:exported="false" >
753-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
754            <intent-filter>
754-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
755                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
755-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
755-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
756            </intent-filter>
757        </receiver>
758        <receiver
758-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
759            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
759-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
760            android:directBootAware="false"
760-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
761            android:enabled="true"
761-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
762            android:exported="true"
762-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
763            android:permission="android.permission.DUMP" >
763-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
764            <intent-filter>
764-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
765                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
765-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
765-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
766            </intent-filter>
767        </receiver>
768
769        <service
769-->[androidx.room:room-runtime:2.5.2] F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:24:9-28:63
770            android:name="androidx.room.MultiInstanceInvalidationService"
770-->[androidx.room:room-runtime:2.5.2] F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:25:13-74
771            android:directBootAware="true"
771-->[androidx.room:room-runtime:2.5.2] F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:26:13-43
772            android:exported="false" />
772-->[androidx.room:room-runtime:2.5.2] F:\R17DevTools\.gradle\caches\8.13\transforms\5e2acb9bee0a5b872b1d515edf8b6f46\transformed\room-runtime-2.5.2\AndroidManifest.xml:27:13-37
773        <service
773-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
774            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
774-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
775            android:enabled="true"
775-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
776            android:exported="false" >
776-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
777            <meta-data
777-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
778                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
778-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
779                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
779-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
780        </service>
781
782        <activity
782-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
783            android:name="androidx.credentials.playservices.HiddenActivity"
783-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
784            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
784-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
785            android:enabled="true"
785-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
786            android:exported="false"
786-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
787            android:fitsSystemWindows="true"
787-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
788            android:theme="@style/Theme.Hidden" >
788-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] F:\R17DevTools\.gradle\caches\8.13\transforms\71352bfc7d2e86eb2cbe49eb213e6f2a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
789        </activity>
790
791        <uses-library
791-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:29:9-31:40
792            android:name="androidx.camera.extensions.impl"
792-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:30:13-59
793            android:required="false" />
793-->[androidx.camera:camera-extensions:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\2b587af44bbcbc68fefe9a64cb17155c\transformed\jetified-camera-extensions-1.5.0-alpha03\AndroidManifest.xml:31:13-37
794
795        <service
795-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:24:9-33:19
796            android:name="androidx.camera.core.impl.MetadataHolderService"
796-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:25:13-75
797            android:enabled="false"
797-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:26:13-36
798            android:exported="false" >
798-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:27:13-37
799            <meta-data
799-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:30:13-32:89
800                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
800-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:31:17-103
801                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
801-->[androidx.camera:camera-camera2:1.5.0-alpha03] F:\R17DevTools\.gradle\caches\8.13\transforms\012659820a206ff178b1dfc31d8ed905\transformed\jetified-camera-camera2-1.5.0-alpha03\AndroidManifest.xml:32:17-86
802        </service>
803
804        <activity
804-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
805            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
805-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
806            android:excludeFromRecents="true"
806-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
807            android:exported="false"
807-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
808            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
808-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
809        <!--
810            Service handling Google Sign-In user revocation. For apps that do not integrate with
811            Google Sign-In, this service will never be started.
812        -->
813        <service
813-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
814            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
814-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
815            android:exported="true"
815-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
816            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
816-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
817            android:visibleToInstantApps="true" />
817-->[com.google.android.gms:play-services-auth:21.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\84319fb3abfb9f4cf22d670fd740c51d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
818
819        <meta-data
819-->[com.github.zjupure:webpdecoder:2.6.4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:10:9-12:43
820            android:name="com.bumptech.glide.integration.webp.WebpGlideModule"
820-->[com.github.zjupure:webpdecoder:2.6.4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:11:13-79
821            android:value="GlideModule" />
821-->[com.github.zjupure:webpdecoder:2.6.4.14.2] F:\R17DevTools\.gradle\caches\8.13\transforms\28d9696d1ba03482c83101bc9186888b\transformed\jetified-webpdecoder-2.6.4.14.2\AndroidManifest.xml:12:13-40
822
823        <receiver
823-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
824            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
824-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
825            android:enabled="true"
825-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
826            android:exported="false" >
826-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
827        </receiver>
828
829        <service
829-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
830            android:name="com.google.android.gms.measurement.AppMeasurementService"
830-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
831            android:enabled="true"
831-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
832            android:exported="false" />
832-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
833        <service
833-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
834            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
834-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
835            android:enabled="true"
835-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
836            android:exported="false"
836-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
837            android:permission="android.permission.BIND_JOB_SERVICE" /> <!-- Needs to be explicitly declared on P+ -->
837-->[com.google.android.gms:play-services-measurement:22.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\b2dbfc7f70830d3d4115ecdb3338302e\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
838        <uses-library
838-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
839            android:name="org.apache.http.legacy"
839-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
840            android:required="false" />
840-->[com.google.android.gms:play-services-maps:17.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\68f1c6167ded75c22bf466a708dea38f\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
841
842        <activity
842-->[com.google.android.gms:play-services-base:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
843            android:name="com.google.android.gms.common.api.GoogleApiActivity"
843-->[com.google.android.gms:play-services-base:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
844            android:exported="false"
844-->[com.google.android.gms:play-services-base:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
845            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
845-->[com.google.android.gms:play-services-base:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\31cc88bd1a811e01477d23dfca8d3955\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
846
847        <provider
847-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
848            android:name="com.google.firebase.provider.FirebaseInitProvider"
848-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
849            android:authorities="com.adtip.app.adtip_app.firebaseinitprovider"
849-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
850            android:directBootAware="true"
850-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
851            android:exported="false"
851-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
852            android:initOrder="100" />
852-->[com.google.firebase:firebase-common:21.0.0] F:\R17DevTools\.gradle\caches\8.13\transforms\a6a23358ad7c61e852a7e630e05b3f0c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
853
854        <uses-library
854-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] F:\R17DevTools\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
855            android:name="android.ext.adservices"
855-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] F:\R17DevTools\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
856            android:required="false" />
856-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] F:\R17DevTools\.gradle\caches\8.13\transforms\32c6230c0475f108e440ef839e9bcc70\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
857
858        <meta-data
858-->[com.google.android.gms:play-services-basement:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
859            android:name="com.google.android.gms.version"
859-->[com.google.android.gms:play-services-basement:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
860            android:value="@integer/google_play_services_version" />
860-->[com.google.android.gms:play-services-basement:18.5.0] F:\R17DevTools\.gradle\caches\8.13\transforms\6ee598c7bab1dd2859d5d01f2229eaf5\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
861
862        <receiver
862-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
863            android:name="androidx.profileinstaller.ProfileInstallReceiver"
863-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
864            android:directBootAware="false"
864-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
865            android:enabled="true"
865-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
866            android:exported="true"
866-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
867            android:permission="android.permission.DUMP" >
867-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
868            <intent-filter>
868-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
869                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
869-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
869-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
870            </intent-filter>
871            <intent-filter>
871-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
872                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
872-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
872-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
873            </intent-filter>
874            <intent-filter>
874-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
875                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
875-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
875-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
876            </intent-filter>
877            <intent-filter>
877-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
878                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
878-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
878-->[androidx.profileinstaller:profileinstaller:1.4.0] F:\R17DevTools\.gradle\caches\8.13\transforms\f568e0d2c8a98331661aa1ad6aad17cf\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
879            </intent-filter>
880        </receiver>
881
882        <service
882-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
883            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
883-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
884            android:exported="false"
884-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
885            android:permission="android.permission.BIND_JOB_SERVICE" >
885-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
886        </service>
887
888        <receiver
888-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
889            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
889-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
890            android:exported="false" /> <!-- Receiver Service -->
890-->[com.google.android.datatransport:transport-runtime:3.3.0] F:\R17DevTools\.gradle\caches\8.13\transforms\bebd9d4895328a627b6a12462063f75b\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
891        <service
891-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:28:9-30:40
892            android:name="app.notifee.core.ReceiverService"
892-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:29:13-60
893            android:exported="false" />
893-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:30:13-37
894
895        <activity
895-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:32:9-38:75
896            android:name="app.notifee.core.NotificationReceiverActivity"
896-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:33:13-73
897            android:excludeFromRecents="true"
897-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:34:13-46
898            android:exported="true"
898-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:35:13-36
899            android:noHistory="true"
899-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:36:13-37
900            android:taskAffinity=""
900-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:37:13-36
901            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
901-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:38:13-72
902
903        <receiver
903-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:46:9-54:20
904            android:name="app.notifee.core.RebootBroadcastReceiver"
904-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:47:13-68
905            android:exported="false" >
905-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:48:13-37
906            <intent-filter>
906-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:49:13-53:29
907                <action android:name="android.intent.action.BOOT_COMPLETED" />
907-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
907-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
908                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
908-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:51:17-82
908-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:51:25-79
909                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
909-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:52:17-82
909-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:52:25-79
910            </intent-filter>
911        </receiver>
912        <receiver
912-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:55:9-61:20
913            android:name="app.notifee.core.AlarmPermissionBroadcastReceiver"
913-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:56:13-77
914            android:exported="true" >
914-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:57:13-36
915            <intent-filter>
915-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:58:13-60:29
916                <action android:name="android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED" />
916-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:59:17-107
916-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:59:25-104
917            </intent-filter>
918        </receiver>
919        <receiver
919-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:62:9-70:20
920            android:name="app.notifee.core.NotificationAlarmReceiver"
920-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:63:13-70
921            android:exported="false" >
921-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:64:13-37
922            <intent-filter>
922-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:49:13-53:29
923                <action android:name="android.intent.action.BOOT_COMPLETED" />
923-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
923-->[androidx.work:work-runtime:2.8.1] F:\R17DevTools\.gradle\caches\8.13\transforms\ce71867dae3b888edaea8cc484faf6ac\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
924                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
924-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:51:17-82
924-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:51:25-79
925                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
925-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:52:17-82
925-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:52:25-79
926            </intent-filter>
927        </receiver> <!-- Broadcast Receiver -->
928        <receiver
928-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:73:9-81:20
929            android:name="app.notifee.core.BlockStateBroadcastReceiver"
929-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:74:13-72
930            android:exported="false" >
930-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:75:13-37
931            <intent-filter>
931-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:76:13-80:29
932                <action android:name="android.app.action.APP_BLOCK_STATE_CHANGED" />
932-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:77:17-85
932-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:77:25-82
933                <action android:name="android.app.action.NOTIFICATION_CHANNEL_BLOCK_STATE_CHANGED" />
933-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:78:17-102
933-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:78:25-99
934                <action android:name="android.app.action.NOTIFICATION_CHANNEL_GROUP_BLOCK_STATE_CHANGED" />
934-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:79:17-108
934-->[app.notifee:core:202108261754] F:\R17DevTools\.gradle\caches\8.13\transforms\d8f641b76267bd5749bd07a7ca418ca7\transformed\jetified-core-202108261754\AndroidManifest.xml:79:25-105
935            </intent-filter>
936        </receiver>
937
938        <activity
938-->[com.jakewharton:process-phoenix:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:8:9-12:75
939            android:name="com.jakewharton.processphoenix.ProcessPhoenix"
939-->[com.jakewharton:process-phoenix:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:9:13-73
940            android:exported="false"
940-->[com.jakewharton:process-phoenix:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:10:13-37
941            android:process=":phoenix"
941-->[com.jakewharton:process-phoenix:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:11:13-39
942            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
942-->[com.jakewharton:process-phoenix:2.1.2] F:\R17DevTools\.gradle\caches\8.13\transforms\1414cc992efd0fca4a6cd3c9d41ee4a2\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:12:13-72
943
944        <meta-data
944-->[com.facebook.soloader:soloader:0.12.1] F:\R17DevTools\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:12:9-14:37
945            android:name="com.facebook.soloader.enabled"
945-->[com.facebook.soloader:soloader:0.12.1] F:\R17DevTools\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:13:13-57
946            android:value="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
946-->[com.facebook.soloader:soloader:0.12.1] F:\R17DevTools\.gradle\caches\8.13\transforms\32503beb4641a05905d0ff4f11c56937\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:14:13-34
947        <activity
947-->[com.google.android.play:core-common:2.0.3] F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
948            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
948-->[com.google.android.play:core-common:2.0.3] F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
949            android:exported="false"
949-->[com.google.android.play:core-common:2.0.3] F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
950            android:stateNotNeeded="true"
950-->[com.google.android.play:core-common:2.0.3] F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
951            android:theme="@style/Theme.PlayCore.Transparent" />
951-->[com.google.android.play:core-common:2.0.3] F:\R17DevTools\.gradle\caches\8.13\transforms\3c2b541c81d6853fcbc79ca7ed76949c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
952    </application>
953
954</manifest>
